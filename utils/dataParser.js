/**
 * 数据解析工具
 * 提供通用的数据解析函数
 */

/**
 * 解析视频数据
 * 将API返回的数据转换为页面需要的格式
 * @param {Array|Object} data - 视频数据
 * @returns {Array} 格式化后的视频数据
 */
export function parseVideoData(data) {
  // 提取视频数组
  const videos = extractVideosArray(data);

  // 如果没有数据，返回空数组
  if (!videos.length) return [];

  // 转换每个视频项
  return videos.map(formatVideoItem);
}

/**
 * 从响应中提取视频数组
 * @param {Array|Object} data - API响应数据
 * @returns {Array} 视频数组
 */
function extractVideosArray(data) {
  // 如果是对象且包含 Items 属性（兼容大小写）
  if (data && typeof data === 'object' && (data.Items || data.items)) {
    return data.Items || data.items;
  }
  // 如果是数组
  if (Array.isArray(data)) {
    return data;
  }
  // 其他情况返回空数组
  return [];
}

/**
 * 格式化视频项
 * @param {Object} video - 视频项
 * @returns {Object} 格式化后的视频项
 */
function formatVideoItem(video) {
  // 如果视频项为空，返回空对象
  if (!video) return {};

  // 兼容大小写字段名
  const id = video.Id || video.id;
  const videoId = video.VideoId || video.videoId;
  const videoName = video.VideoName || video.videoName || video.title || '';
  const videoPic = video.VideoPic || video.videoPic || video.poster || 'https://placehold.co/300x400';
  const videoType = video.VideoType || video.videoType || '';
  const videoTag = video.VideoTag || video.videoTag || '';
  const videoScore = video.VideoScore || video.videoScore || 0;
  const videoHits = video.VideoHits || video.videoHits || 0;
  const videoDuration = video.VideoDuration || video.videoDuration || '';
  const isRecommended = video.IsRecommended || video.isRecommended || false;
  const videoAddTime = video.VideoAddTime || video.videoAddTime || '';
  const videoUpdateTime = video.VideoUpdateTime || video.videoUpdateTime || '';
  const videoActor = video.VideoActor || video.videoActor || '';
  const videoArea = video.VideoArea || video.videoArea || '';
  const videoLang = video.VideoLang || video.videoLang || '';
  const videoDirector = video.VideoDirector || video.videoDirector || '';
  const videoRemarks = video.VideoRemarks || video.videoRemarks || '';
  const videoYear = video.VideoYear || video.videoYear;

  // 提取年份
  const year = getVideoYear({
    videoYear: videoYear,
    videoAddTime: videoAddTime
  });

  // 处理标签
  const tags = videoTag ? videoTag.split(',') : [];

  // 返回格式化后的视频项
  return {
    id: id,
    videoId: videoId,
    title: videoName,
    poster: videoPic,
    videoName: videoName,
    videoPic: videoPic,
    videoType: videoType,
    videoTag: videoTag,
    videoScore: videoScore,
    videoHits: videoHits,
    videoDuration: videoDuration,
    isRecommended: isRecommended,
    videoAddTime: videoAddTime,
    videoUpdateTime: videoUpdateTime,
    year: year,
    tags: tags,
    actor: videoActor,
    area: videoArea,
    lang: videoLang,
    director: videoDirector
  };
}

/**
 * 从视频数据中提取年份
 * @param {Object} video - 视频数据
 * @returns {string} 年份
 */
function getVideoYear(video) {
  // 如果有明确的年份字段，直接使用
  if (video.videoYear) return video.videoYear;

  // 尝试从发布时间提取年份
  if (video.videoAddTime) {
    const match = video.videoAddTime.match(/(\d{4})/);
    if (match) return match[1];
  }

  // 默认返回空字符串
  return '';
}
