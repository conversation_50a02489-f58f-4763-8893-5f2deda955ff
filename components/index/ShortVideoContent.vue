<template>
  <div class="box">
    <swiper class="swiper" circular :indicator-dots="indicatorDots" :autoplay="autoplay" :interval="interval"
            :duration="duration">
      <swiper-item v-for="(item, index) in displaySwiperVideos" :key="index">
        <view class="swiper-item">
          <navigator :url="`/pages/view/view?id=${item.id}`">
            <view class="swiper-img">
              <image :src="item.poster" mode="aspectFill" />
            </view>
            <view class="biaoti">{{ item.title }}</view>
          </navigator>
        </view>
      </swiper-item>
      <!-- 如果没有任何数据，显示占位图 -->
      <swiper-item v-if="displaySwiperVideos.length === 0">
        <view class="swiper-item">
          <view class="swiper-img">
            <image src="https://placehold.co/750x375" mode="aspectFill" />
          </view>
          <view class="biaoti">正在加载推荐内容...</view>
        </view>
      </swiper-item>
    </swiper>
  </div>

  <view class="box">
    <view class="title">热门视频</view>
    <ul class="list">
      <li v-for="(item, index) in hotVideos" :key="index">
        <navigator :url="`/pages/view/view?id=${item.id}`">
          <view class="cover">
            <view class="img">
              <image :src="item.poster" mode="aspectFill"/>
            </view>
            <view class="video-info">
              <view class="pingfen">{{ item.score ? item.score.toFixed(1) : '' }}</view>
              <view class="renqitime">
                <span>{{ formatNumber(item.hits) }}</span>
                <span>{{ item.remarks || item.videoDuration || '' }}</span>
              </view>
            </view>
          </view>
          <view class="biaoti">{{ item.title }}</view>
        </navigator>
      </li>
      <!-- 如果没有数据，显示占位项 -->
      <li v-if="hotVideos.length === 0" v-for="i in 6" :key="'placeholder-'+i">
        <view class="cover">
          <view class="img">
            <image src="https://placehold.co/500x283" mode="aspectFill"/>
          </view>
          <view class="video-info">
            <view class="pingfen"> </view>
            <view class="renqitime">
              <span>--</span>
              <span>--:--</span>
            </view>
          </view>
        </view>
        <view class="biaoti">正在加载热门内容...</view>
      </li>
    </ul>
  </view>

  <view class="box">
    <view class="title">为你推荐</view>
    <ul class="list">
      <li v-for="(item, index) in recommendedVideos" :key="index">
        <navigator :url="`/pages/view/view?id=${item.id}`">
          <view class="cover">
            <view class="img">
              <image :src="item.poster" mode="aspectFill"/>
            </view>
            <view class="video-info">
              <view class="pingfen">{{ item.score ? item.score.toFixed(1) : '' }}</view>
              <view class="renqitime">
                <span>{{ formatNumber(item.hits) }}</span>
                <span>{{ item.remarks || item.videoDuration || '' }}</span>
              </view>
            </view>
          </view>
          <view class="biaoti">{{ item.title }}</view>
        </navigator>
      </li>
      <!-- 如果没有数据，显示占位项 -->
      <li v-if="recommendedVideos.length === 0" v-for="i in 6" :key="'placeholder-'+i">
        <view class="cover">
          <view class="img">
            <image src="https://placehold.co/500x283" mode="aspectFill"/>
          </view>
          <view class="video-info">
            <view class="pingfen"> </view>
            <view class="renqitime">
              <span>--</span>
              <span>--:--</span>
            </view>
          </view>
        </view>
        <view class="biaoti">正在加载推荐内容...</view>
      </li>
    </ul>

    <!-- 加载更多提示 -->
    <view v-if="recommendedVideos.length > 0" class="load-more">
      <view v-if="loadingMore" class="loading">加载中...</view>
      <view v-else-if="!hasMore" class="no-more">没有更多数据了</view>
    </view>
  </view>


</template>

<script>
import { getRandomVideos, parseVideoData, getVideoList } from '@/api/video';
import logger from '@/utils/logger';

export default {
  name: 'ShortVideoContent',
  props: {
    // 视频类型，如电影、电视剧等
    videoType: {
      type: String,
      default: null
    },
    // 视频类型ID，用于加载标签数据
    videoTypeId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      background: ['color1', 'color2', 'color3'],
      indicatorDots: true,
      autoplay: true,
      interval: 2000,
      duration: 500,
      loading: false,
      loadingMore: false,
      dataLoaded: false,
      hasMore: true,
      currentPage: 1,
      pageSize: 18, // 每次加载18个视频
      swiperVideos: [], // swiper部分的推荐视频
      hotVideos: [], // 热门视频
      recommendedVideos: [], // 为你推荐的视频
      _loadMoreTimer: null, // 防抖定时器
      _lastLoadTime: null // 上次加载时间
    }
  },
  computed: {
    /**
     * 显示在swiper中的视频列表
     * 直接使用swiperVideos，不使用备选方案，确保内容不重复
     */
    displaySwiperVideos() {
      return this.swiperVideos || [];
    }
  },
  mounted() {
    // 添加事件监听器
    // 先移除可能存在的监听器，避免重复
    uni.$off('page-reach-bottom', this.handleReachBottom);
    // 监听页面滚动事件
    uni.$on('page-reach-bottom', this.handleReachBottom);
  },

  beforeDestroy() {
    // 移除监听
    uni.$off('page-reach-bottom', this.handleReachBottom);
  },

  created() {
    // 在created钩子中不加载数据，避免与activated钩子重复加载
    // 数据加载将在activated钩子中进行
  },

  // 组件被keep-alive缓存时，再次激活时触发
  activated() {
    // 重新添加事件监听器，确保只有一个监听器实例
    uni.$off('page-reach-bottom', this.handleReachBottom);
    uni.$on('page-reach-bottom', this.handleReachBottom);

    // 检查是否需要重新加载数据
    if (!this.dataLoaded || this.swiperVideos.length === 0 || this.hotVideos.length === 0) {
      // 重置组件状态
      this.loading = false;
      this.dataLoaded = false;

      // 加载swiper和热门视频数据
      this.loadData();
    }
  },

  // 组件被keep-alive缓存时，即将切换出去时触发
  deactivated() {
    // 移除事件监听器，避免内存泄漏
    uni.$off('page-reach-bottom', this.handleReachBottom);
  },
  // 监听属性变化
  watch: {
    // 监听 videoType 变化，重新加载数据
    videoType(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.dataLoaded = false;
        this.loadData();
      }
    }
  },
  methods: {
    changeIndicatorDots(e) {
      this.indicatorDots = !this.indicatorDots
    },
    changeAutoplay(e) {
      this.autoplay = !this.autoplay
    },
    intervalChange(e) {
      this.interval = e.target.value
    },
    durationChange(e) {
      this.duration = e.target.value
    },

    /**
     * 处理页面触底事件
     */
    handleReachBottom() {
      // 如果没有在加载中且有更多数据，则加载更多
      if (!this.loading && !this.loadingMore && this.hasMore) {
        this.loadRecommendedVideos(false);
      }
    },
    /**
     * 格式化数字，大于1000显示为K
     */
    formatNumber(num) {
      if (!num) return '0';
      const n = parseInt(num);
      if (n >= 10000) {
        return (n / 10000).toFixed(1) + '万';
      } else if (n >= 1000) {
        return (n / 1000).toFixed(1) + 'K';
      }
      return n.toString();
    },

    /**
     * 加载数据
     */
    async loadData() {
      // 如果正在加载，则不重复加载
      if (this.loading) {
        return;
      }

      try {
        // 设置加载状态
        this.loading = true;

        // 并行加载swiper和热门视频
        await Promise.all([
          this.loadSwiperVideos(),  // 加载swiper推荐视频
          this.loadHotVideos()      // 加载热门视频
        ]);

        // 单独加载推荐视频，避免重复调用
        await this._loadRecommendedVideosImpl(true);  // 加载推荐视频，首次加载

        // 标记数据已加载
        this.dataLoaded = true;
        this.currentPage = 1;
        this.hasMore = true;
      } catch (error) {
        // 错误处理
      } finally {
        this.loading = false;
      }
    },

    /**
     * 加载Swiper推荐视频（使用独立的接口调用，获取6条推荐数据）
     */
    async loadSwiperVideos() {
      try {
        // 使用 getRandomVideos 获取幻灯片推荐视频数据（与“为你推荐”分开调用）
        const response = await getRandomVideos({
          maxResultCount: 6,
          skipCount: 0,
          videoType: this.videoType,  // 使用当前栏目
          sorting: 'Random'           // 随机排序
        });

        // 处理响应数据
        if ((Array.isArray(response) && response.length > 0) || (response && response.items && response.items.length > 0)) {
          let videos = parseVideoData(response);
          this.swiperVideos = videos;
        } else {
          this.swiperVideos = [];
        }
      } catch (error) {
        this.swiperVideos = [];
      }
    },

    /**
     * 加载热门视频（按点击排行的6条数据）
     */
    async loadHotVideos() {
      try {
        // 使用 getVideoList 获取热门视频数据
        const response = await getVideoList({
          page: 1,
          pageSize: 6,
          videoType: this.videoType,  // 使用当前栏目
          sorting: 'VideoHits desc'   // 按点击量降序排序
        });

        // 处理响应数据
        if ((Array.isArray(response) && response.length > 0) || (response && response.items && response.items.length > 0)) {
          let videos = parseVideoData(response);

          // 不进行额外过滤，直接使用API返回的数据
          this.hotVideos = videos;
        } else {
          this.hotVideos = [];
        }
      } catch (error) {
        this.hotVideos = [];
      }
    },



    /**
     * 加载推荐视频（随机数据，支持无限下拉）
     * @param {boolean} isFirstLoad - 是否是首次加载
     */
    async loadRecommendedVideos(isFirstLoad = false) {
      // 如果正在加载更多或者没有更多数据，则跳过
      if (!isFirstLoad && (this.loadingMore || !this.hasMore)) {
        return;
      }

      // 使用防抖定时器，确保短时间内只调用一次
      if (!isFirstLoad) {
        // 清除之前的定时器
        if (this._loadMoreTimer) {
          clearTimeout(this._loadMoreTimer);
        }

        // 设置新的定时器，延迟300毫秒执行
        return new Promise((resolve) => {
          this._loadMoreTimer = setTimeout(() => {
            // 调用实际的加载方法
            this._loadRecommendedVideosImpl(isFirstLoad).then(resolve);
          }, 300);
        });
      }

      // 首次加载直接执行
      return this._loadRecommendedVideosImpl(isFirstLoad);
    },

    /**
     * 实际加载推荐视频的方法
     * @param {boolean} isFirstLoad - 是否是首次加载
     */
    async _loadRecommendedVideosImpl(isFirstLoad = false) {
      // 记录当前时间，防止短时间内重复调用
      const now = Date.now();
      if (!isFirstLoad && this._lastLoadTime && (now - this._lastLoadTime < 1000)) {
        return;
      }
      this._lastLoadTime = now;

      try {
        // 设置加载状态
        if (isFirstLoad) {
          this.loading = true;
          this.currentPage = 1;
          this.hasMore = true;
        } else {
          this.loadingMore = true;
        }

        // 使用 getRandomVideos 获取推荐视频数据（从第6个开始，避免与幻灯片数据重复）
        const response = await getRandomVideos({
          maxResultCount: this.pageSize,
          skipCount: isFirstLoad ? 6 : (6 + (this.currentPage - 1) * this.pageSize),
          videoType: this.videoType,  // 使用当前栏目
          sorting: 'Random'           // 随机排序
        });

        // 处理响应数据
        if ((Array.isArray(response) && response.length > 0) || (response && response.items && response.items.length > 0)) {
          let newVideos = parseVideoData(response);

          // 判断是否还有更多数据
          this.hasMore = newVideos.length >= this.pageSize;

          // 如果是首次加载，直接设置数据，否则添加到现有数据中
          if (isFirstLoad) {
            this.recommendedVideos = newVideos;
          } else {
            this.recommendedVideos = [...this.recommendedVideos, ...newVideos];
          }

          // 更新分页信息
          this.currentPage++;
        } else {
          if (isFirstLoad) {
            this.recommendedVideos = [];
          }
          this.hasMore = false;
        }
      } catch (error) {
        if (isFirstLoad) {
          this.recommendedVideos = [];
        }
        this.hasMore = false;
      } finally {
        // 延迟一下再隐藏加载状态，让用户能看到加载动画
        setTimeout(() => {
          if (isFirstLoad) {
            this.loading = false;
          } else {
            this.loadingMore = false;
          }
        }, 300);
      }
    }
  }
}
</script>

<style scoped>

.cover{
  height: auto;
  position: relative;
}

.cover .img {
  position: relative;
  width: 100%;
  /* 使用固定的 16:9 的宽高比例 */
  padding-bottom: 56.25%; /* 9/16 = 0.5625 = 56.25% */
  overflow: hidden;
}

.cover .img image {
  width: 100%;
  height: 100%;
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  object-fit: cover; /* 确保图片填充整个容器且不变形 */
}

/* swiper图片容器样式 */
.swiper-img {
  position: relative;
  width: 100%;
  /* 使用固定的 16:9 的宽高比例 */
  padding-bottom: 56.25%; /* 9/16 = 0.5625 = 56.25% */
  overflow: hidden;
}

.swiper-img image {
  width: 100%;
  height: 100%;
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  object-fit: cover; /* 确保图片填充整个容器且不变形 */
}

/* 加载更多提示样式 */
.load-more {
  text-align: center;
  padding: 20rpx 0;
  color: #969699;
  font-size: 24rpx;
}

.loading {
  display: inline-block;
  position: relative;
  padding-left: 40rpx;
}

.loading:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  margin-top: -10rpx;
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid #969699;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.no-more {
  position: relative;
  display: inline-block;
}

.no-more:before, .no-more:after {
  content: '';
  position: absolute;
  top: 50%;
  width: 60rpx;
  height: 1px;
  background: #969699;
}

.no-more:before {
  left: -80rpx;
}

.no-more:after {
  right: -80rpx;
}

</style>
