<template>
  <view>
    <!-- 加载状态 -->
    <view v-if="loading && !videos.length" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载数据...</text>
    </view>

    <!-- 错误提示 -->
    <view v-else-if="error && !videos.length" class="error-container">
      <text class="error-text">{{ error }}</text>
      <view class="error-retry" @click="loadData">点击重试</view>
    </view>

    <!-- 视频列表 -->
    <ul v-else class="discoverlist">
      <li v-for="(item, index) in videos" :key="index">
        <view class="biaoti">{{ item.title }}</view>
        <view class="cover" @click="playVideo(index)">
          <!-- 视频播放器，仅在当前视频被选中播放时显示 -->
          <view v-if="currentPlayingIndex === index" class="video-container" :style="getVideoContainerStyle(item)">
            <iframe
              :ref="`player-${index}`"
              :data-player-index="index"
              src="about:blank"
              frameborder="0"
              class="video-player"
              style="width: 100%; height: 100%;"
              border="0"
              marginwidth="0"
              marginheight="0"
              scrolling="no"
              allowfullscreen
              mozallowfullscreen="mozallowfullscreen"
              msallowfullscreen="msallowfullscreen"
              oallowfullscreen="oallowfullscreen"
              webkitallowfullscreen="webkitallowfullscreen"
              @error="handlePlayerError"
            ></iframe>
          </view>

          <!-- 视频封面，仅在视频未播放时显示 -->
          <view v-else>
            <view class="img">
              <image :src="item.poster" mode="widthFix"/>
            </view>
            <view class="play">
              <image src="/static/bofang.png" mode="widthFix"/>
            </view>
            <view class="info">
              <span>{{ item.remarks || item.videoDuration || '' }}</span>
            </view>
          </view>
        </view>
        <view class="bottom">
          <span><i class="icon guankan"></i>{{ item.hits || 0 }}</span>
          <span><i class="icon zan"></i>{{ item.up || 0 }}</span>
        </view>
      </li>
    </ul>

    <!-- 加载更多提示 -->
    <view v-if="videos.length > 0" class="load-more">
      <view v-if="loadingMore" class="loading">加载中...</view>
      <view v-else-if="!hasMore" class="no-more">没有更多数据了</view>
    </view>
  </view>
</template>

<script>
import { getRandomVideos, parseVideoData, getVideoDetail } from '@/api/video';
import { getCachedNavigationList } from '@/api/navigationCache';
import { parseVodPlayUrl } from '@/utils/vodPlayer';
import logger from '@/utils/logger';

export default {
  name: 'tuijianContent',
  data() {
    return {
      videos: [],
      loading: false,
      loadingMore: false,
      error: null,
      hasMore: true,
      currentPage: 1,
      pageSize: 20,
      shortVideoCategories: [],
      currentPlayingIndex: null, // 当前正在播放的视频索引
      scrollPosition: 0, // 记录滚动位置
      isFullscreen: false, // 是否处于全屏模式
    };
  },
  mounted() {
    this.loadData();

    // 监听页面滚动，实现无限滚动
    uni.$on('page-reach-bottom', this.loadMoreVideos);

    // 监听下拉刷新事件
    uni.$on('page-pull-down-refresh', this.loadData);

    // 监听全屏变化事件
    this.setupFullscreenListener();

    // 监听页面滚动，记录滚动位置
    window.addEventListener('scroll', this.handleScroll, { passive: true });
  },
  beforeDestroy() {
    // 移除事件监听
    uni.$off('page-reach-bottom', this.loadMoreVideos);
    uni.$off('page-pull-down-refresh', this.loadData);

    // 移除全屏变化事件监听
    this.removeFullscreenListener();

    // 移除滚动事件监听
    window.removeEventListener('scroll', this.handleScroll);

    // 清理播放器
    this.pauseCurrentVideo();
  },
  methods: {
    // 加载数据
    async loadData() {
      try {
        this.loading = true;
        this.error = null;

        // 获取所有 extraParams: "0" 的栏目
        await this.loadShortVideoCategories();

        // 从各个栏目获取视频
        await this.loadVideosFromCategories();

      } catch (error) {
        logger.error('加载发现页数据失败:', error);
        this.error = '加载数据失败，请稍后重试';
      } finally {
        this.loading = false;
      }
    },

    // 获取所有 extraParams 中 isHorizontalLayout 为 true 的栏目
    async loadShortVideoCategories() {
      const navigationResponse = await getCachedNavigationList();
      this.shortVideoCategories = navigationResponse
        .filter(nav => {
          // 兼容大小写字段名
          const isH5Display = nav.IsH5Display || nav.isH5Display;
          if (!isH5Display) return false;

          // 解析 extraParams，兼容大小写字段名
          const extraParams = nav.ExtraParams || nav.extraParams;
          let extraParamsObj = null;
          try {
            if (extraParams && typeof extraParams === 'string') {
              extraParamsObj = JSON.parse(extraParams);
            }
          } catch (error) {
            console.error('解析extraParams失败:', error, extraParams);
            return false;
          }

          // 筛选 isHorizontalLayout 为 true 的项目
          return extraParamsObj && extraParamsObj.isHorizontalLayout === true;
        })
        .map(nav => nav.CategotyName || nav.categotyName);

      console.log('短视频栏目:', this.shortVideoCategories);
    },

    // 从各个栏目获取视频
    async loadVideosFromCategories() {
      if (!this.shortVideoCategories.length) {
        this.error = '没有找到短视频栏目';
        return;
      }

      // 设置需要获取的视频总数
      const totalVideosNeeded = this.pageSize;

      // 随机打乱栏目顺序，增强随机性
      const shuffledCategories = this.shuffleArray([...this.shortVideoCategories]);

      // 计算每个栏目需要获取的视频数量
      let videosPerCategory;
      let categoriesToUse;

      if (shuffledCategories.length <= totalVideosNeeded) {
        // 每个栏目至少获取一个视频，剩下的平均分配
        videosPerCategory = Math.ceil(totalVideosNeeded / shuffledCategories.length);
        categoriesToUse = shuffledCategories;
      } else {
        // 随机选择totalVideosNeeded个栏目，每个获取一个视频
        videosPerCategory = 1;
        categoriesToUse = shuffledCategories.slice(0, totalVideosNeeded);
      }

      // 并行请求所有选中栏目的视频
      const requests = categoriesToUse.map(category => {
        return getRandomVideos({
          maxResultCount: videosPerCategory,
          skipCount: 0,
          videoType: category,
          sorting: 'Random'
        });
      });

      // 等待所有请求完成
      const responses = await Promise.all(requests);

      // 处理所有响应
      const allVideos = [];
      responses.forEach(response => {
        if (response && (Array.isArray(response) || response.items)) {
          const items = Array.isArray(response) ? response : response.items;
          allVideos.push(...items);
        }
      });

      // 如果没有获取到视频，尝试使用默认方式
      if (allVideos.length === 0) {
        const response = await getRandomVideos({
          maxResultCount: totalVideosNeeded,
          skipCount: 0,
          sorting: 'Random'
        });

        if (response && (Array.isArray(response) || response.items)) {
          const items = Array.isArray(response) ? response : response.items;
          allVideos.push(...items);
        }
      }

      // 解析视频数据
      const parsedVideos = parseVideoData(allVideos);

      // 随机打乱并设置视频列表
      this.videos = this.shuffleArray(parsedVideos);

      // 更新分页信息
      this.currentPage = 1;
      this.hasMore = true;
    },

    // 加载更多视频
    async loadMoreVideos() {
      if (this.loadingMore || !this.hasMore) return;

      try {
        this.loadingMore = true;

        // 增加页码
        this.currentPage++;

        // 从各个栏目获取更多视频
        const totalVideosNeeded = this.pageSize;

        // 随机打乱栏目顺序，增强随机性
        const shuffledCategories = this.shuffleArray([...this.shortVideoCategories]);

        // 计算每个栏目需要获取的视频数量
        let videosPerCategory;
        let categoriesToUse;

        if (shuffledCategories.length <= totalVideosNeeded) {
          videosPerCategory = Math.ceil(totalVideosNeeded / shuffledCategories.length);
          categoriesToUse = shuffledCategories;
        } else {
          videosPerCategory = 1;
          categoriesToUse = shuffledCategories.slice(0, totalVideosNeeded);
        }

        // 并行请求所有选中栏目的视频
        const requests = categoriesToUse.map(category => {
          return getRandomVideos({
            maxResultCount: videosPerCategory,
            skipCount: (this.currentPage - 1) * videosPerCategory,
            videoType: category,
            sorting: 'Random'
          });
        });

        // 等待所有请求完成
        const responses = await Promise.all(requests);

        // 处理所有响应
        const newVideos = [];
        responses.forEach(response => {
          if (response && (Array.isArray(response) || response.items)) {
            const items = Array.isArray(response) ? response : response.items;
            newVideos.push(...items);
          }
        });

        // 解析视频数据
        const parsedVideos = parseVideoData(newVideos);

        // 随机打乱并添加到视频列表
        const shuffledNewVideos = this.shuffleArray(parsedVideos);

        // 过滤掉已经存在的视频（根据ID去重）
        const existingIds = this.videos.map(v => v.id);
        const uniqueNewVideos = shuffledNewVideos.filter(v => !existingIds.includes(v.id));

        // 添加到视频列表
        this.videos = [...this.videos, ...uniqueNewVideos];

        // 判断是否还有更多数据
        this.hasMore = uniqueNewVideos.length > 0;

      } catch (error) {
        logger.error('加载更多视频失败:', error);
      } finally {
        this.loadingMore = false;
      }
    },



    // 播放视频
    async playVideo(index) {
      // 如果有其他视频正在播放，先暂停
      if (this.currentPlayingIndex !== null && this.currentPlayingIndex !== index) {
        this.pauseCurrentVideo();
      }

      // 如果点击的是当前正在播放的视频，则暂停
      if (this.currentPlayingIndex === index) {
        this.pauseCurrentVideo();
        return;
      }

      // 显示加载中提示
      uni.showLoading({
        title: '加载中...'
      });

      try {
        // 记录当前滚动位置
        this.scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
        console.log('记录当前滚动位置:', this.scrollPosition);

        // 设置当前播放的视频索引
        this.currentPlayingIndex = index;

        // 获取视频详情
        const video = this.videos[index];
        const videoDetail = await getVideoDetail(video.id);

        if (!videoDetail || !videoDetail.videoPlayUrl) {
          throw new Error('获取视频详情失败或无播放地址');
        }

        // 解析播放地址，参考view页面的实现
        const playerItems = parseVodPlayUrl(videoDetail.videoPlayUrl)[0];
        if (!playerItems || !playerItems.length) {
          throw new Error('解析播放地址失败');
        }

        // 获取完整的播放地址
        const playUrl = videoDetail.videoPlayServer + playerItems[0].url;
        logger.log('最终播放地址:', playUrl);

        // 等待DOM更新，确保Iframe已经渲染
        this.$nextTick(() => {
          // 获取iframe元素
          const iframeRef = this.$refs[`player-${index}`];
          const iframe = Array.isArray(iframeRef) ? iframeRef[0] : iframeRef;

          if (iframe) {
            // 直接设置src属性
            logger.log('设置播放地址:', playUrl);
            iframe.src = playUrl;
          } else {
            // 如果无法获取iframe元素，尝试使用DOM API
            setTimeout(() => {
              const domIframe = document.querySelector(`[data-player-index="${index}"]`);
              if (domIframe) {
                console.log('使用DOM API设置播放地址:', playUrl);
                domIframe.src = playUrl;
              } else {
                throw new Error('无法获取播放器元素');
              }
            }, 300);
          }
        });

        // 记录播放历史
        this.addToPlayHistory(video);
      } catch (error) {
        logger.error('播放视频失败:', error);
        this.pauseCurrentVideo();
        uni.showToast({
          title: '播放失败，请重试',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 暂停当前播放的视频
    pauseCurrentVideo() {
      if (this.currentPlayingIndex === null) return;

      // 重置当前播放的视频索引
      this.currentPlayingIndex = null;
    },

    // 处理播放器错误
    handlePlayerError() {
      logger.error('播放器错误');

      // 显示错误提示
      uni.showToast({
        title: '视频播放失败，请尝试其他视频',
        icon: 'none'
      });

      // 暂停当前视频
      this.pauseCurrentVideo();
    },

    // 添加到播放历史
    addToPlayHistory(video) {
      try {
        // 构建历史记录对象
        const historyItem = {
          id: video.id,
          videoName: video.videoName || video.title,
          videoPic: video.videoPic || video.poster,
          videoType: video.videoType,
          timestamp: Date.now()
        };

        // 添加extraParams信息，所有发现页的视频都是短视频
        // 使用JSON字符串格式的extraParams
        historyItem.extraParams = '{"isHorizontalLayout": true}';

        // 获取现有历史
        let history = uni.getStorageSync('playHistory') || [];
        if (typeof history === 'string') {
          try {
            history = JSON.parse(history);
          } catch (e) {
            history = [];
          }
        }

        // 移除相同视频的旧记录
        history = history.filter(item => item.id !== historyItem.id);

        // 添加新记录到开头
        history.unshift(historyItem);

        // 限制历史记录数量
        if (history.length > 50) {
          history = history.slice(0, 50);
        }

        // 保存历史
        uni.setStorageSync('playHistory', JSON.stringify(history));
      } catch (error) {
        console.error('保存播放历史失败:', error);
      }
    },

    // 获取视频容器样式，始终使用 16:9 的宽高比
    getVideoContainerStyle(video) {
      // 始终使用 16:9 的宽高比，不再根据视频实际尺寸调整
      const aspectRatio = 9 / 16; // 16:9 的宽高比是 9/16 = 0.5625

      // 计算高度，使用视口宽度乘以宽高比
      const height = `${aspectRatio * 100}vw`; // 56.25vw

      return {
        height: height,
        maxHeight: '80vh' // 限制最大高度，避免过高
      };
    },

    // 设置全屏监听器
    setupFullscreenListener() {
      // 监听各种浏览器的全屏变化事件
      document.addEventListener('fullscreenchange', this.handleFullscreenChange);
      document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange);
      document.addEventListener('mozfullscreenchange', this.handleFullscreenChange);
      document.addEventListener('MSFullscreenChange', this.handleFullscreenChange);
    },

    // 移除全屏监听器
    removeFullscreenListener() {
      document.removeEventListener('fullscreenchange', this.handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange);
    },

    // 处理全屏变化
    handleFullscreenChange() {
      // 检测当前是否处于全屏模式
      const isFullscreen = !!(document.fullscreenElement || document.webkitFullscreenElement ||
                           document.mozFullScreenElement || document.msFullscreenElement);

      console.log('全屏状态变化:', isFullscreen);

      // 如果从全屏退出
      if (this.isFullscreen && !isFullscreen) {
        console.log('退出全屏，恢复滚动位置:', this.scrollPosition);
        // 等待一小段时间再恢复滚动位置，确保浏览器已完成全屏退出操作
        setTimeout(() => {
          window.scrollTo({
            top: this.scrollPosition,
            behavior: 'auto'
          });
        }, 100);
      }

      // 更新全屏状态
      this.isFullscreen = isFullscreen;
    },

    // 处理页面滚动
    handleScroll() {
      // 只在非全屏模式下记录滚动位置
      if (!this.isFullscreen) {
        this.scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
      }
    },

    // 数组随机打乱
    shuffleArray(array) {
      const newArray = [...array];
      for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
      }
      return newArray;
    }
  }
};
</script>


<style scoped>

.discoverlist{
  .cover{
    height: auto;
    position: relative;
  }
}

/* 视频播放器容器 */
.video-container {
  width: 100%;
  /* 高度将通过 getVideoContainerStyle 方法动态设置 */
  min-height: 56.25vw; /* 最小高度使用 16:9 的宽高比 (9/16 = 0.5625 = 56.25%) */
  position: relative;
  background: #000;
  overflow: hidden;
  transition: height 0.3s ease; /* 添加高度变化的过渡效果 */
}

/* 视频播放器 */
.video-player {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

ul li {
  .biaoti {
    font-size: 32rpx;
    padding: 30rpx 20rpx 16rpx 20rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .imglist {
    display: flex;
    gap: 20rpx;
    padding: 20rpx;

    .img1 {
      width: calc(33.3vw - 25rpx);
      height: calc(33.3vw - 25rpx);
      overflow: hidden;
      flex: 1;
      flex-shrink: 0;
      position: relative;

      navigator {
        display: block;
        height: 100%;
      }

      .mark {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        background: #000000b3;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 40rpx;
        color: #fff;
        font-weight: 600;
      }

      image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
      }
    }

  }

  .img {
    position: relative;
    width: 100%;
    /* 使用固定的 16:9 的宽高比例 */
    padding-bottom: 56.25%; /* 9/16 = 0.5625 = 56.25% */
    overflow: hidden;
  }
  .img image {
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    object-fit: cover; /* 确保图片填充整个容器且不变形 */
  }

  .play {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 999;

    image {
      width: 50px;
    }
  }

  .info {
    position: absolute;
    width: calc(100% - 20rpx);
    padding: 0 10rpx;
    bottom: 0;
    height: 80rpx;
    font-size: 24rpx;
    background: linear-gradient(0deg, #101012, rgba(16, 16, 18, 0));
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

.bottom {
  display: flex;
  justify-content: space-around;
  background: #ffffff1a;
  color: #6f6f71;
  font-size: 26rpx;
  padding: 20rpx 0;

  span {
    display: flex;
    align-items: center;
  }

  span:nth-child(2) i.icon:before {
    opacity: 0.4;
  }

  i.icon:before {
    width: 48rpx;
    height: 48rpx;
  }
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(255, 116, 61, 0.2);
  border-top-color: #ff743d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #969699;
}

/* 错误提示样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.error-text {
  font-size: 28rpx;
  color: #969699;
  text-align: center;
  margin-bottom: 20rpx;
}

.error-retry {
  padding: 10rpx 30rpx;
  background: rgba(255, 116, 61, 0.1);
  border: 1px solid #ff743d;
  border-radius: 30rpx;
  color: #ff743d;
  font-size: 24rpx;
}

/* 加载更多样式 */
.load-more {
  text-align: center;
  padding: 20rpx 0;
  color: #969699;
  font-size: 24rpx;
}

</style>